#!/usr/bin/env python3
"""
Playwright Stealth 机器人检测绕过脚本
使用 playwright-stealth 插件来绕过 bot.sannysoft.com 的机器人检测
"""

import asyncio
import time
from playwright.async_api import async_playwright
from playwright_stealth import Stealth


async def test_bot_detection():
    """
    测试机器人检测绕过功能
    """
    async with async_playwright() as p:
        # 启动浏览器，使用更真实的配置
        browser = await p.chromium.launch(
            headless=False,  # 设置为 False 以便观察浏览器行为
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection',
                '--no-first-run',
                '--no-default-browser-check',
                '--no-pings',
                '--password-store=basic',
                '--use-mock-keychain',
                '--disable-component-extensions-with-background-pages',
                '--disable-background-networking',
                '--disable-component-update',
                '--disable-client-side-phishing-detection',
                '--disable-sync',
                '--metrics-recording-only',
                '--disable-default-apps',
                '--mute-audio',
                '--no-zygote',
                '--disable-gpu-sandbox',
                '--disable-software-rasterizer'
            ]
        )
        
        # 创建新的浏览器上下文，模拟真实用户
        context = await browser.new_context(
            viewport={'width': 1366, 'height': 768},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='zh-CN',
            timezone_id='Asia/Shanghai',
            permissions=['geolocation'],
            geolocation={'latitude': 39.9042, 'longitude': 116.4074},  # 北京坐标
            extra_http_headers={
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Cache-Control': 'max-age=0'
            }
        )
        
        # 创建新页面
        page = await context.new_page()
        
        # 应用 stealth 插件
        stealth = Stealth()
        await stealth.apply_stealth_async(context)
        
        # 添加额外的反检测措施
        await page.add_init_script("""
            // 移除 webdriver 属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 修改 plugins 长度
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 修改 languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            // 添加 chrome 对象
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            
            // 修改 permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // 伪造 screen 属性
            Object.defineProperty(screen, 'colorDepth', {
                get: () => 24,
            });
            
            Object.defineProperty(screen, 'pixelDepth', {
                get: () => 24,
            });
        """)
        
        print("正在访问机器人检测网站...")
        
        try:
            # 访问机器人检测网站
            await page.goto('https://bot.sannysoft.com/', wait_until='networkidle', timeout=30000)
            
            # 等待页面完全加载
            await page.wait_for_timeout(3000)
            
            print("页面已加载，正在分析检测结果...")
            
            # 等待检测完成
            await page.wait_for_timeout(5000)
            
            # 获取页面标题
            title = await page.title()
            print(f"页面标题: {title}")
            
            # 截图保存结果
            screenshot_path = "bot_detection_result.png"
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"检测结果截图已保存到: {screenshot_path}")
            
            # 尝试获取检测结果
            try:
                # 查找可能的检测结果元素
                results = await page.query_selector_all('td')
                if results:
                    print("\n检测结果:")
                    for i, result in enumerate(results[:20]):  # 只显示前20个结果
                        text = await result.inner_text()
                        if text.strip():
                            print(f"  {i+1}. {text.strip()}")
                
                # 查找红色或绿色的检测结果
                red_elements = await page.query_selector_all('[style*="color: red"], [style*="color:#ff0000"], .red')
                green_elements = await page.query_selector_all('[style*="color: green"], [style*="color:#00ff00"], .green')
                
                if red_elements:
                    print(f"\n发现 {len(red_elements)} 个红色警告（可能被检测到）")
                    for i, elem in enumerate(red_elements[:5]):
                        text = await elem.inner_text()
                        if text.strip():
                            print(f"  警告 {i+1}: {text.strip()}")
                
                if green_elements:
                    print(f"\n发现 {len(green_elements)} 个绿色通过项")
                    for i, elem in enumerate(green_elements[:5]):
                        text = await elem.inner_text()
                        if text.strip():
                            print(f"  通过 {i+1}: {text.strip()}")
                            
            except Exception as e:
                print(f"获取检测结果时出错: {e}")
            
            # 保持页面打开一段时间以便观察
            print("\n页面将保持打开状态 10 秒，您可以手动查看检测结果...")
            await page.wait_for_timeout(10000)
            
        except Exception as e:
            print(f"访问网站时出错: {e}")
            # 即使出错也截图
            try:
                await page.screenshot(path="error_screenshot.png")
                print("错误截图已保存到: error_screenshot.png")
            except:
                pass
        
        finally:
            # 关闭浏览器
            await browser.close()
            print("浏览器已关闭")


async def main():
    """
    主函数
    """
    print("=" * 60)
    print("Playwright Stealth 机器人检测绕过测试")
    print("=" * 60)
    print("正在启动浏览器...")
    
    start_time = time.time()
    await test_bot_detection()
    end_time = time.time()
    
    print(f"\n测试完成，总耗时: {end_time - start_time:.2f} 秒")
    print("=" * 60)


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
