#!/usr/bin/env python3
"""
Playwright Stealth 机器人检测绕过脚本
使用 playwright-stealth 插件来绕过 bot.sannysoft.com 的机器人检测
"""

import asyncio
import time
from playwright.async_api import async_playwright
from playwright_stealth import Stealth


async def test_bot_detection():
    """
    测试机器人检测绕过功能
    """
    # 使用推荐的 Stealth 用法
    stealth = Stealth()
    async with stealth.use_async(async_playwright()) as p:
        # 启动浏览器，使用更真实的配置
        browser = await p.chromium.launch(
            headless=False,  # 设置为 False 以便观察浏览器行为
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-features=TranslateUI,VizDisplayCompositor',
                '--disable-ipc-flooding-protection',
                '--no-first-run',
                '--no-default-browser-check',
                '--no-pings',
                '--password-store=basic',
                '--use-mock-keychain',
                '--disable-component-extensions-with-background-pages',
                '--disable-background-networking',
                '--disable-component-update',
                '--disable-client-side-phishing-detection',
                '--disable-sync',
                '--metrics-recording-only',
                '--disable-default-apps',
                '--mute-audio',
                '--no-zygote',
                '--disable-gpu-sandbox',
                '--disable-software-rasterizer',
                '--exclude-switches=enable-automation',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
        )
        
        # 创建新的浏览器上下文，模拟真实用户
        context = await browser.new_context(
            viewport={'width': 1366, 'height': 768},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='zh-CN',
            timezone_id='Asia/Shanghai',
            permissions=['geolocation'],
            geolocation={'latitude': 39.9042, 'longitude': 116.4074},  # 北京坐标
            extra_http_headers={
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Cache-Control': 'max-age=0'
            }
        )
        
        # 创建新页面
        page = await context.new_page()
        
        # Stealth 已经通过 use_async 自动应用到所有页面

        # 添加更强的反检测脚本到上下文
        await context.add_init_script("""
            // 在所有页面加载前执行的反检测代码
            (() => {
                'use strict';

                // 1. 彻底移除 webdriver 属性
                const removeWebdriver = () => {
                    try {
                        // 删除 Navigator 原型上的 webdriver
                        if ('webdriver' in Navigator.prototype) {
                            delete Navigator.prototype.webdriver;
                        }

                        // 删除 navigator 实例上的 webdriver
                        if ('webdriver' in navigator) {
                            delete navigator.webdriver;
                        }

                        // 重新定义为 undefined
                        Object.defineProperty(Navigator.prototype, 'webdriver', {
                            get: () => undefined,
                            enumerable: false,
                            configurable: false
                        });

                        // 确保 navigator.webdriver 始终返回 undefined
                        Object.defineProperty(navigator, 'webdriver', {
                            get: () => undefined,
                            enumerable: false,
                            configurable: false
                        });
                    } catch (e) {
                        console.log('webdriver removal failed:', e);
                    }
                };

                // 2. 修复 PluginArray 类型检测
                const fixPluginArray = () => {
                    try {
                        // 确保 plugins 是真正的 PluginArray 实例
                        const originalPlugins = navigator.plugins;

                        // 创建一个新的 PluginArray 实例
                        const newPluginArray = Object.create(PluginArray.prototype);

                        // 复制原有插件
                        for (let i = 0; i < originalPlugins.length; i++) {
                            Object.defineProperty(newPluginArray, i, {
                                get: () => originalPlugins[i],
                                enumerable: true,
                                configurable: false
                            });
                        }

                        // 设置长度
                        Object.defineProperty(newPluginArray, 'length', {
                            get: () => originalPlugins.length,
                            enumerable: false,
                            configurable: false
                        });

                        // 添加方法
                        newPluginArray.item = function(index) {
                            return this[index] || null;
                        };

                        newPluginArray.namedItem = function(name) {
                            for (let i = 0; i < this.length; i++) {
                                if (this[i] && this[i].name === name) {
                                    return this[i];
                                }
                            }
                            return null;
                        };

                        newPluginArray.refresh = function() {};

                        // 确保 constructor 正确
                        Object.defineProperty(newPluginArray, 'constructor', {
                            get: () => PluginArray,
                            enumerable: false,
                            configurable: false
                        });

                        // 确保 toString 正确
                        newPluginArray.toString = function() {
                            return '[object PluginArray]';
                        };

                        // 替换 navigator.plugins
                        Object.defineProperty(navigator, 'plugins', {
                            get: () => newPluginArray,
                            enumerable: true,
                            configurable: false
                        });

                    } catch (e) {
                        console.log('PluginArray fix failed:', e);
                    }
                };

                // 3. 移除其他自动化痕迹
                const removeAutomationTraces = () => {
                    try {
                        // 移除 Chrome DevTools 相关属性
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy;

                        // 移除 Playwright 相关属性
                        delete window.__playwright;
                        delete window.__pw_manual;
                        delete window.__PW_inspect;

                        // 移除其他自动化工具痕迹
                        delete window.callPhantom;
                        delete window._phantom;
                        delete window.phantom;
                        delete window.__nightmare;
                        delete window._selenium;
                        delete window.webdriver;
                        delete window.driver;
                        delete window.domAutomation;
                        delete window.domAutomationController;
                        delete window.__webdriver_script_fn;
                        delete window.__driver_evaluate;
                        delete window.__webdriver_evaluate;
                        delete window.__selenium_evaluate;
                        delete window.__fxdriver_evaluate;
                        delete window.__driver_unwrapped;
                        delete window.__webdriver_unwrapped;
                        delete window.__selenium_unwrapped;
                        delete window.__fxdriver_unwrapped;
                        delete window.__webdriver_script_func;

                    } catch (e) {
                        console.log('Automation traces removal failed:', e);
                    }
                };

                // 执行所有修复
                removeWebdriver();
                fixPluginArray();
                removeAutomationTraces();

                // 定期检查并重新应用修复
                setInterval(() => {
                    removeWebdriver();
                    removeAutomationTraces();
                }, 100);

            })();
        """)
        
        # 添加额外的反检测措施
        await page.add_init_script("""
            // 在页面加载之前就移除 webdriver 属性
            (() => {
                const originalDescriptor = Object.getOwnPropertyDescriptor(Navigator.prototype, 'webdriver');
                if (originalDescriptor) {
                    delete Navigator.prototype.webdriver;
                }

                // 彻底移除所有可能的 webdriver 引用
                delete Object.getPrototypeOf(navigator).webdriver;
                delete navigator.__proto__.webdriver;
                delete navigator.webdriver;

                // 重新定义 webdriver 属性为 undefined
                Object.defineProperty(Navigator.prototype, 'webdriver', {
                    get: () => undefined,
                    set: () => {},
                    enumerable: false,
                    configurable: false
                });

                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    set: () => {},
                    enumerable: false,
                    configurable: false
                });
            })();

            // 创建真实的 PluginArray 对象
            (() => {
                // 保存原始的 PluginArray 构造函数
                const OriginalPluginArray = window.PluginArray;

                // 创建一个真实的 PluginArray 实例
                const createRealPluginArray = () => {
                    // 创建一个真正的 PluginArray 实例
                    const pluginArray = Object.create(PluginArray.prototype);

                    // 添加一些常见的插件
                    const plugins = [
                        {
                            name: 'Chrome PDF Plugin',
                            filename: 'internal-pdf-viewer',
                            description: 'Portable Document Format',
                            length: 1,
                            0: { type: 'application/pdf', suffixes: 'pdf', description: 'Portable Document Format' }
                        },
                        {
                            name: 'Chrome PDF Viewer',
                            filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                            description: '',
                            length: 1,
                            0: { type: 'application/pdf', suffixes: 'pdf', description: '' }
                        },
                        {
                            name: 'Native Client',
                            filename: 'internal-nacl-plugin',
                            description: '',
                            length: 2,
                            0: { type: 'application/x-nacl', suffixes: '', description: 'Native Client Executable' },
                            1: { type: 'application/x-pnacl', suffixes: '', description: 'Portable Native Client Executable' }
                        }
                    ];

                    // 设置插件数组的属性
                    Object.defineProperty(pluginArray, 'length', {
                        get: () => plugins.length,
                        enumerable: true,
                        configurable: false
                    });

                    // 添加插件到数组
                    plugins.forEach((plugin, index) => {
                        Object.defineProperty(pluginArray, index, {
                            get: () => plugin,
                            enumerable: true,
                            configurable: false
                        });
                        Object.defineProperty(pluginArray, plugin.name, {
                            get: () => plugin,
                            enumerable: false,
                            configurable: false
                        });
                    });

                    // 添加 PluginArray 的方法
                    pluginArray.item = function(index) {
                        return this[index] || null;
                    };

                    pluginArray.namedItem = function(name) {
                        return this[name] || null;
                    };

                    pluginArray.refresh = function() {};

                    // 确保 constructor 指向正确的构造函数
                    Object.defineProperty(pluginArray, 'constructor', {
                        get: () => PluginArray,
                        enumerable: false,
                        configurable: false
                    });

                    // 确保 toString 返回正确的字符串
                    pluginArray.toString = function() {
                        return '[object PluginArray]';
                    };

                    return pluginArray;
                };

                // 替换 navigator.plugins
                const realPluginArray = createRealPluginArray();
                Object.defineProperty(navigator, 'plugins', {
                    get: () => realPluginArray,
                    enumerable: true,
                    configurable: false
                });
            })();

            // 修改 languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
                enumerable: true,
                configurable: true
            });

            // 添加 chrome 对象
            if (!window.chrome) {
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {
                        return {
                            requestTime: Date.now() - 1000,
                            startLoadTime: Date.now() - 800,
                            commitLoadTime: Date.now() - 600,
                            finishDocumentLoadTime: Date.now() - 400,
                            finishLoadTime: Date.now() - 200,
                            firstPaintTime: Date.now() - 100,
                            firstPaintAfterLoadTime: 0,
                            navigationType: 'Other',
                            wasFetchedViaSpdy: false,
                            wasNpnNegotiated: false,
                            npnNegotiatedProtocol: '',
                            wasAlternateProtocolAvailable: false,
                            connectionInfo: 'http/1.1'
                        };
                    },
                    csi: function() {
                        return {
                            startE: Date.now(),
                            onloadT: Date.now(),
                            pageT: Date.now() - 1000,
                            tran: 15
                        };
                    },
                    app: {
                        isInstalled: false,
                        InstallState: {
                            DISABLED: 'disabled',
                            INSTALLED: 'installed',
                            NOT_INSTALLED: 'not_installed'
                        },
                        RunningState: {
                            CANNOT_RUN: 'cannot_run',
                            READY_TO_RUN: 'ready_to_run',
                            RUNNING: 'running'
                        }
                    }
                };
            }

            // 修改 permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // 伪造 screen 属性
            Object.defineProperty(screen, 'colorDepth', {
                get: () => 24,
                enumerable: true
            });

            Object.defineProperty(screen, 'pixelDepth', {
                get: () => 24,
                enumerable: true
            });

            // 隐藏自动化相关的属性
            Object.defineProperty(window, 'outerHeight', {
                get: () => window.innerHeight,
            });

            Object.defineProperty(window, 'outerWidth', {
                get: () => window.innerWidth,
            });

            // 移除可能暴露自动化的其他属性
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """)
        
        print("正在访问机器人检测网站...")
        
        try:
            # 访问机器人检测网站
            await page.goto('https://bot.sannysoft.com/', wait_until='networkidle', timeout=30000)
            
            # 等待页面完全加载
            await page.wait_for_timeout(3000)
            
            print("页面已加载，正在分析检测结果...")
            
            # 等待检测完成
            await page.wait_for_timeout(5000)
            
            # 获取页面标题
            title = await page.title()
            print(f"页面标题: {title}")
            
            # 截图保存结果
            screenshot_path = "bot_detection_result.png"
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"检测结果截图已保存到: {screenshot_path}")
            
            # 尝试获取检测结果
            try:
                # 查找可能的检测结果元素
                results = await page.query_selector_all('td')
                if results:
                    print("\n检测结果:")
                    for i, result in enumerate(results[:20]):  # 只显示前20个结果
                        text = await result.inner_text()
                        if text.strip():
                            print(f"  {i+1}. {text.strip()}")
                
                # 查找红色或绿色的检测结果
                red_elements = await page.query_selector_all('[style*="color: red"], [style*="color:#ff0000"], .red')
                green_elements = await page.query_selector_all('[style*="color: green"], [style*="color:#00ff00"], .green')
                
                if red_elements:
                    print(f"\n发现 {len(red_elements)} 个红色警告（可能被检测到）")
                    for i, elem in enumerate(red_elements[:5]):
                        text = await elem.inner_text()
                        if text.strip():
                            print(f"  警告 {i+1}: {text.strip()}")
                
                if green_elements:
                    print(f"\n发现 {len(green_elements)} 个绿色通过项")
                    for i, elem in enumerate(green_elements[:5]):
                        text = await elem.inner_text()
                        if text.strip():
                            print(f"  通过 {i+1}: {text.strip()}")
                            
            except Exception as e:
                print(f"获取检测结果时出错: {e}")
            
            # 保持页面打开一段时间以便观察
            print("\n页面将保持打开状态 10 秒，您可以手动查看检测结果...")
            await page.wait_for_timeout(10000)
            
        except Exception as e:
            print(f"访问网站时出错: {e}")
            # 即使出错也截图
            try:
                await page.screenshot(path="error_screenshot.png")
                print("错误截图已保存到: error_screenshot.png")
            except:
                pass
        
        finally:
            # 关闭浏览器
            await browser.close()
            print("浏览器已关闭")


async def main():
    """
    主函数
    """
    print("=" * 60)
    print("Playwright Stealth 机器人检测绕过测试")
    print("=" * 60)
    print("正在启动浏览器...")
    
    start_time = time.time()
    await test_bot_detection()
    end_time = time.time()
    
    print(f"\n测试完成，总耗时: {end_time - start_time:.2f} 秒")
    print("=" * 60)


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
